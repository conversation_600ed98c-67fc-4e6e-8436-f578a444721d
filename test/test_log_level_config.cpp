#include <iostream>
#include <fstream>
#include <string>
#include "logger.hpp"
#include "config.hpp"

// 测试日志等级配置的程序
int main() {
    std::cout << "测试日志等级配置功能..." << std::endl;
    
    // 测试1: 测试日志等级字符串转换函数
    std::cout << "\n=== 测试1: 日志等级字符串转换 ===" << std::endl;
    
    std::vector<std::string> test_levels = {
        "trace", "debug", "info", "warn", "warning", 
        "error", "err", "critical", "crit", "off", "invalid"
    };
    
    for (const auto& level_str : test_levels) {
        auto level = stringToLogLevel(level_str);
        std::cout << "字符串 '" << level_str << "' -> 等级: " 
                  << static_cast<int>(level) << " (" 
                  << spdlog::level::to_string_view(level) << ")" << std::endl;
    }
    
    // 测试2: 创建测试配置文件
    std::cout << "\n=== 测试2: 创建测试配置文件 ===" << std::endl;
    
    std::string test_config_content = R"({
  "io_settings": {
    "engine_paths": "data/test.trt",
    "table_paths": "data/test.csv",
    "logging": {
      "log_file_path": "logs/test_runtime.log",
      "log_level": "debug"
    }
  }
})";
    
    std::ofstream test_config("test_config.json");
    test_config << test_config_content;
    test_config.close();
    
    std::cout << "创建测试配置文件: test_config.json" << std::endl;
    
    // 测试3: 使用配置文件初始化日志
    std::cout << "\n=== 测试3: 从配置文件初始化日志 ===" << std::endl;
    
    bool init_result = initLoggerFromConfig("test_config.json");
    std::cout << "日志初始化结果: " << (init_result ? "成功" : "失败") << std::endl;
    
    // 测试4: 测试不同等级的日志输出
    std::cout << "\n=== 测试4: 测试不同等级的日志输出 ===" << std::endl;
    
    spdlog::trace("这是一条 TRACE 级别的日志");
    spdlog::debug("这是一条 DEBUG 级别的日志");
    spdlog::info("这是一条 INFO 级别的日志");
    spdlog::warn("这是一条 WARN 级别的日志");
    spdlog::error("这是一条 ERROR 级别的日志");
    spdlog::critical("这是一条 CRITICAL 级别的日志");
    
    std::cout << "日志输出完成，请检查 logs/test_runtime.log 文件" << std::endl;
    
    // 测试5: 测试ConfigManager的日志等级获取
    std::cout << "\n=== 测试5: 测试ConfigManager的日志等级获取 ===" << std::endl;
    
    try {
        ConfigManager config("test_config.json");
        auto log_level = config.get<std::string>("/io_settings/logging/log_level");
        auto log_path = config.get<std::string>("/io_settings/logging/log_file_path");
        
        if (log_level && log_path) {
            std::cout << "从ConfigManager获取的日志等级: " << *log_level << std::endl;
            std::cout << "从ConfigManager获取的日志路径: " << *log_path << std::endl;
            
            // 测试重新配置日志
            spdlog::level::level_enum file_level = stringToLogLevel(*log_level);
            initLogger(*log_path, file_level);
            
            std::cout << "重新配置日志系统完成" << std::endl;
            
            // 再次测试日志输出
            spdlog::trace("重新配置后的 TRACE 日志");
            spdlog::debug("重新配置后的 DEBUG 日志");
            spdlog::info("重新配置后的 INFO 日志");
            
        } else {
            std::cout << "无法从ConfigManager获取日志配置" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "ConfigManager测试失败: " << e.what() << std::endl;
    }
    
    // 测试6: 测试不同日志等级的配置
    std::cout << "\n=== 测试6: 测试不同日志等级的配置 ===" << std::endl;
    
    std::vector<std::string> test_log_levels = {"trace", "debug", "info", "warn", "error"};
    
    for (const auto& level : test_log_levels) {
        std::cout << "\n测试日志等级: " << level << std::endl;
        
        // 创建临时配置
        std::string temp_config = R"({
  "io_settings": {
    "logging": {
      "log_file_path": "logs/test_)" + level + R"(.log",
      "log_level": ")" + level + R"("
    }
  }
})";
        
        std::ofstream temp_file("temp_config_" + level + ".json");
        temp_file << temp_config;
        temp_file.close();
        
        // 初始化日志
        if (initLoggerFromConfig("temp_config_" + level + ".json")) {
            spdlog::trace("TRACE 消息 (等级: " + level + ")");
            spdlog::debug("DEBUG 消息 (等级: " + level + ")");
            spdlog::info("INFO 消息 (等级: " + level + ")");
            spdlog::warn("WARN 消息 (等级: " + level + ")");
            spdlog::error("ERROR 消息 (等级: " + level + ")");
        }
    }
    
    std::cout << "\n所有测试完成!" << std::endl;
    std::cout << "请检查 logs/ 目录下的日志文件以验证不同等级的输出" << std::endl;
    
    return 0;
}
