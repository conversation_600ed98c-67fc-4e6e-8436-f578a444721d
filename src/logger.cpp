#include "logger.hpp"
// #include "config.hpp"
#include <iostream>
#include <fstream>
#include <string>
#include <algorithm>
#include <cctype>

// #include <filesystem>

// 日志等级字符串转换函数
spdlog::level::level_enum stringToLogLevel(const std::string& level_str) {
    std::string lower_level = level_str;
    std::transform(lower_level.begin(), lower_level.end(), lower_level.begin(), ::tolower);

    if (lower_level == "trace") return spdlog::level::trace;
    else if (lower_level == "debug") return spdlog::level::debug;
    else if (lower_level == "info") return spdlog::level::info;
    else if (lower_level == "warn" || lower_level == "warning") return spdlog::level::warn;
    else if (lower_level == "error" || lower_level == "err") return spdlog::level::err;
    else if (lower_level == "critical" || lower_level == "crit") return spdlog::level::critical;
    else if (lower_level == "off") return spdlog::level::off;
    else {
        std::cerr << "未知的日志等级: " << level_str << "，使用默认等级 info" << std::endl;
        return spdlog::level::info;
    }
}

void initLogger(const std::string& log_path, spdlog::level::level_enum file_level) {
    try {

        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        // 日志文件轮换: 文件名, 最大文件大小(50MB), 保留文件个数(10个)
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path, 50 * 1024 * 1024, 10);

        // 控制台只显示错误级别以上的日志
        console_sink->set_level(spdlog::level::err);
        // 文件日志使用配置的等级
        file_sink->set_level(file_level);

        auto logger = std::make_shared<spdlog::logger>("multi_sink",
                            spdlog::sinks_init_list{console_sink, file_sink});

        // 设置logger的总体等级为两个sink中较低的等级
        spdlog::level::level_enum overall_level = std::min(spdlog::level::err, file_level);
        logger->set_level(overall_level);
        logger->set_pattern("[%m-%d %H:%M:%S.%e] [%^%l%$] %v");

        spdlog::set_default_logger(logger);
        spdlog::flush_on(spdlog::level::info);

        spdlog::info("日志系统初始化完成 - 文件等级: {}, 控制台等级: err", spdlog::level::to_string_view(file_level));

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "日志初始化失败: " << ex.what() << std::endl;
    }
}

// 使用默认配置的日志初始化版本
void initLogger() {
    try {
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::info);

        auto logger = std::make_shared<spdlog::logger>("console_logger", console_sink);
        logger->set_level(spdlog::level::info);
        logger->set_pattern("[%Y-%m-%d %H:%M:%S] [%^%l%$] %v");

        spdlog::set_default_logger(logger);
        spdlog::flush_on(spdlog::level::info);

        spdlog::info("日志系统初始化完成（控制台模式）");

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "日志初始化失败: " << ex.what() << std::endl;
    }
}

// 直接从配置文件初始化日志系统
bool initLoggerFromConfig(const std::string& config_path) {
    try {
        // 简单的JSON解析来获取日志路径和等级
        std::ifstream config_file(config_path);
        if (!config_file.is_open()) {
            std::cerr << "无法打开配置文件: " << config_path << std::endl;
            return false;
        }

        std::string line;
        std::string log_path;
        std::string log_level = "info";  // 默认等级
        bool found_log_path = false;
        bool found_log_level = false;

        // 简单的文本搜索方式查找日志路径和等级
        while (std::getline(config_file, line)) {
            if (line.find("\"log_file_path\"") != std::string::npos) {
                // 提取路径值
                size_t start = line.find(":") + 1;
                size_t first_quote = line.find("\"", start);
                size_t second_quote = line.find("\"", first_quote + 1);

                if (first_quote != std::string::npos && second_quote != std::string::npos) {
                    log_path = line.substr(first_quote + 1, second_quote - first_quote - 1);
                    found_log_path = true;
                }
            }
            else if (line.find("\"log_level\"") != std::string::npos) {
                // 提取日志等级值
                size_t start = line.find(":") + 1;
                size_t first_quote = line.find("\"", start);
                size_t second_quote = line.find("\"", first_quote + 1);

                if (first_quote != std::string::npos && second_quote != std::string::npos) {
                    log_level = line.substr(first_quote + 1, second_quote - first_quote - 1);
                    found_log_level = true;
                }
            }

            // 如果都找到了就可以退出循环
            if (found_log_path && found_log_level) {
                break;
            }
        }

        if (!found_log_path || log_path.empty()) {
            std::cerr << "配置文件中未找到有效的日志路径，使用控制台模式" << std::endl;
            initLogger();  // 回退到控制台模式
            return true;
        }

        // 转换日志等级
        spdlog::level::level_enum file_level = stringToLogLevel(log_level);

        std::cout << "从配置文件加载日志设置 - 路径: " << log_path
                  << ", 等级: " << log_level << std::endl;

        // 使用找到的路径和等级初始化日志
        initLogger(log_path, file_level);
        return true;

    } catch (const std::exception& e) {
        std::cerr << "从配置文件初始化日志失败: " << e.what() << std::endl;
        initLogger();  // 回退到控制台模式
        return true;
    }
}
