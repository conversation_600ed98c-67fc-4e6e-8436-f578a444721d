// config.cpp
#include "config.hpp"
#include <fstream>
#include <iostream>
#include <filesystem>
#include <shared_mutex>
#include <mutex>

namespace fs = std::filesystem;

ConfigManager::ConfigManager(const std::string& config_path) {
    loadDefaultConfig();

    if (!config_path.empty()) {
        nlohmann::json custom_config;
        if (!loadJsonFromFile(config_path, custom_config)) {
            std::cerr << "Failed to load config from " << config_path << ", using default config" << std::endl;
        } else {
            std::cout << "Loaded config from " << config_path << std::endl;
            mergeConfig(custom_config);
        }
    }
}

// bool ConfigManager::loadFromFile(const std::string& config_path) {
//     std::ifstream file(config_path);
//     if (!file.is_open()) {
//         return false;
//     }

//     try {
//         config_data_ = nlohmann::json::parse(file);
//         return true;
//     } catch (const std::exception& e) {
//         std::cerr << "Error parsing config file: " << e.what() << std::endl;
//         return false;
//     }
// }

bool ConfigManager::loadJsonFromFile(const std::string& config_path, nlohmann::json& out_json) {
    std::ifstream file(config_path);
    if (!file.is_open()) {
        return false;
    }

    try {
        out_json = nlohmann::json::parse(file);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing config file: " << e.what() << std::endl;
        return false;
    }
}

void ConfigManager::loadDefaultConfig() {
    std::ifstream file("config/default_config.json");
    if (!file.is_open()) {
        std::cerr << "Failed to load default config, exiting" << std::endl;
        exit(1);
    }

    try {
        config_data_ = nlohmann::json::parse(file);
    } catch (const std::exception& e) {
        std::cerr << "Error parsing default config file: " << e.what() << std::endl;
        exit(1);
    }
}

bool ConfigManager::saveConfig(const std::string& config_path) {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);

    std::ofstream file(config_path);
    if (!file.is_open()) {
        return false;
    }

    try {
        file << config_data_.dump(4);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error writing config file: " << e.what() << std::endl;
        return false;
    }
}

template<typename T>
std::optional<T> ConfigManager::get(const std::string& key) const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);

    try {
        const auto ptr = nlohmann::json::json_pointer(key);
        if (!config_data_.contains(ptr)) {
            return std::nullopt;
        }
        return config_data_[ptr].get<T>();
    } catch (const nlohmann::json::exception& e) {
        std::cerr << "[Config] Error getting '" << key << "': " << e.what() << std::endl;
        return std::nullopt;
    }
}

template<typename T>
void ConfigManager::set(const std::string& key, const T& value) {
    std::lock_guard<std::shared_mutex> lock(config_mutex_);

    try {
        config_data_[nlohmann::json::json_pointer(key)] = value;
    } catch (const std::exception& e) {
        std::cerr << "Error setting config value: " << e.what() << std::endl;
    }
}

const nlohmann::json& ConfigManager::getConfig() const {
    std::shared_lock<std::shared_mutex> lock(config_mutex_);
    return config_data_;
}

void ConfigManager::mergeConfig(const nlohmann::json& other_config) {
    std::lock_guard<std::shared_mutex> lock(config_mutex_);
    config_data_.merge_patch(other_config);
}

// 显式模板实例化
template std::optional<int> ConfigManager::get(const std::string& key) const;
template std::optional<float> ConfigManager::get(const std::string& key) const;
template std::optional<std::string> ConfigManager::get(const std::string& key) const;
template std::optional<bool> ConfigManager::get(const std::string& key) const;

template void ConfigManager::set(const std::string& key, const int& value);
template void ConfigManager::set(const std::string& key, const float& value);
template void ConfigManager::set(const std::string& key, const std::string& value);
template void ConfigManager::set(const std::string& key, const bool& value);
