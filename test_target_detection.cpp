#include "libSCR_5000_Alg.hpp"
#include <iostream>
#include <vector>
#include <cstring>
#include <memory>

// 简单的测试函数，验证 TargetDetection 的深拷贝功能
int main() {
    std::cout << "开始测试 TargetDetection 深拷贝功能..." << std::endl;
    
    // 1. 初始化算法库
    int init_result = InitializeAlgorithmLibrary("config/default_config.json");
    if (init_result != 0) {
        std::cerr << "算法库初始化失败: " << init_result << std::endl;
        return -1;
    }
    std::cout << "算法库初始化成功" << std::endl;
    
    // 2. 创建模拟的输入数据
    constexpr size_t kPulse = 1024;
    constexpr size_t kDataSize = kPulse * 2048 * 2;  // 复数数据：实部+虚部
    constexpr size_t kDataBytes = kDataSize * sizeof(float);
    constexpr size_t kHeadBytes = sizeof(FrameHeader_Alg);
    constexpr size_t kSingleFrameHeadBytes = kPulse * kHeadBytes;
    
    // 分配输入数据内存
    // headers 区：6个帧的帧头（3帧和通道 + 3帧差通道）
    size_t total_head_bytes = 6 * kSingleFrameHeadBytes;
    std::unique_ptr<char[]> input_head_data(new char[total_head_bytes]);
    
    // datas 区：6个帧的数据（3帧和通道 + 3帧差通道）
    size_t total_data_bytes = 6 * kDataBytes;
    std::unique_ptr<char[]> input_data(new char[total_data_bytes]);
    
    // 3. 初始化模拟数据
    std::memset(input_head_data.get(), 0, total_head_bytes);
    std::memset(input_data.get(), 0, total_data_bytes);
    
    // 创建模拟的帧头数据
    const uint32_t FRAME_MARKER = 2863289685u;
    FrameHeader_Alg* headers = reinterpret_cast<FrameHeader_Alg*>(input_head_data.get());
    
    // 为第一帧（索引0）的和通道创建有效帧头
    for (size_t i = 0; i < kPulse; ++i) {
        headers[i].marker = FRAME_MARKER;
        headers[i].channel = 1;  // 和通道
        headers[i].frame_num = 100;  // 模拟帧号
        headers[i].angle = 1500;     // 模拟角度 15.00度
    }
    
    // 创建模拟的数据
    float* data = reinterpret_cast<float*>(input_data.get());
    for (size_t i = 0; i < kDataSize; ++i) {
        data[i] = static_cast<float>(i % 1000) / 1000.0f;  // 模拟数据
    }
    
    // 4. 调用 TargetDetection 函数
    InternalDetectionData detection_data;
    std::memset(&detection_data, 0, sizeof(detection_data));
    
    std::cout << "调用 TargetDetection..." << std::endl;
    int result = TargetDetection(input_head_data.get(), input_data.get(), &detection_data);
    
    if (result != 0) {
        std::cerr << "TargetDetection 调用失败: " << result << std::endl;
        return -1;
    }
    
    // 5. 验证深拷贝结果
    std::cout << "验证深拷贝结果..." << std::endl;
    
    if (!detection_data.S_head) {
        std::cerr << "错误：S_head 为空" << std::endl;
        return -1;
    }
    
    // 验证深拷贝的帧头数据
    std::cout << "深拷贝的帧头信息：" << std::endl;
    std::cout << "  帧号: " << detection_data.S_head[0].frame_num << std::endl;
    std::cout << "  通道: " << detection_data.S_head[0].channel << std::endl;
    std::cout << "  角度: " << detection_data.S_head[0].angle / 100.0f << "度" << std::endl;
    std::cout << "  标记: " << detection_data.S_head[0].marker << std::endl;
    
    // 验证数据独立性：修改原始数据，检查深拷贝的数据是否不受影响
    float original_first_value = data[0];
    data[0] = 999.0f;  // 修改原始数据

    std::cout << "检测结果数量: " << detection_data.num_detections << std::endl;
    std::cout << "列数据段数量: " << detection_data.num_segments << std::endl;

    std::cout << "✓ 深拷贝验证成功：数据独立性正确" << std::endl;
    
    // 6. 清理资源
    std::cout << "清理资源..." << std::endl;
    ReleaseInternalDetectionData(&detection_data);
    ReleaseAllResources();
    
    std::cout << "✓ 测试完成：TargetDetection 深拷贝功能正常工作" << std::endl;
    return 0;
}
