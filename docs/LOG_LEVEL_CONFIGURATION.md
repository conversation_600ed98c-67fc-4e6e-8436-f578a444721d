# 日志等级配置说明

## 概述

本文档说明如何通过配置文件控制日志系统的输出等级。日志系统支持将不同等级的日志分别输出到控制台和文件，其中文件日志等级可通过配置文件进行配置。

## 配置文件格式

在配置文件的 `io_settings/logging` 部分可以设置日志相关参数：

```json
{
  "io_settings": {
    "logging": {
      "log_file_path": "logs/runtime.log",
      "log_level": "info"
    }
  }
}
```

### 配置参数说明

- `log_file_path`: 日志文件的路径
- `log_level`: 文件日志的等级，支持以下值：
  - `"trace"`: 最详细的日志，包含所有信息
  - `"debug"`: 调试信息，用于开发和问题诊断
  - `"info"`: 一般信息，默认等级
  - `"warn"` 或 `"warning"`: 警告信息
  - `"error"` 或 `"err"`: 错误信息
  - `"critical"` 或 `"crit"`: 严重错误
  - `"off"`: 关闭日志输出

## 日志输出行为

### 控制台输出
- 控制台始终只显示 `error` 级别及以上的日志
- 这样可以保持控制台输出的简洁性

### 文件输出
- 文件日志等级由配置文件中的 `log_level` 参数决定
- 文件会记录指定等级及以上的所有日志
- 日志文件使用轮换机制：
  - 单个文件最大 50MB
  - 最多保留 10 个历史文件

## 使用示例

### 1. 开发调试模式
```json
{
  "io_settings": {
    "logging": {
      "log_file_path": "logs/debug_runtime.log",
      "log_level": "debug"
    }
  }
}
```
这种配置会在文件中记录详细的调试信息，适合开发和问题诊断。

### 2. 生产环境模式
```json
{
  "io_settings": {
    "logging": {
      "log_file_path": "logs/production_runtime.log",
      "log_level": "warn"
    }
  }
}
```
这种配置只记录警告和错误信息，适合生产环境。

### 3. 错误监控模式
```json
{
  "io_settings": {
    "logging": {
      "log_file_path": "logs/error_runtime.log",
      "log_level": "error"
    }
  }
}
```
这种配置只记录错误和严重错误，适合错误监控。

## 配置文件示例

项目中提供了以下配置文件示例：

- `config/default_config.json`: 默认配置（info 等级）
- `config/custom_config.json`: 自定义配置（info 等级）
- `config/debug_config.json`: 调试配置（debug 等级）
- `config/error_config.json`: 错误监控配置（error 等级）

## 运行时日志等级更改

日志等级在算法库初始化时设置，如果需要更改日志等级：

1. 修改配置文件中的 `log_level` 参数
2. 重新启动应用程序

## 日志格式

日志输出格式为：
```
[MM-dd HH:MM:SS.mmm] [LEVEL] message
```

其中：
- `MM-dd`: 月-日
- `HH:MM:SS.mmm`: 时:分:秒.毫秒
- `LEVEL`: 日志等级（TRACE/DEBUG/INFO/WARN/ERROR/CRITICAL）
- `message`: 日志消息内容

## 测试日志配置

可以使用提供的测试程序验证日志配置：

```bash
# 编译并运行日志配置测试
chmod +x test_log_config.sh
./test_log_config.sh
```

测试程序会：
1. 测试日志等级字符串转换功能
2. 创建不同等级的配置文件
3. 验证日志输出到文件的行为
4. 检查生成的日志文件内容

## 注意事项

1. **日志文件路径**: 确保日志文件路径的目录存在且有写入权限
2. **磁盘空间**: 注意监控日志文件占用的磁盘空间
3. **性能影响**: 较低的日志等级（如 trace）会产生大量日志，可能影响性能
4. **配置验证**: 如果配置文件中的日志等级无效，系统会使用默认的 info 等级

## 故障排除

### 问题：日志文件没有生成
- 检查日志文件路径是否正确
- 确认目录是否存在且有写入权限
- 查看控制台是否有错误信息

### 问题：日志等级不生效
- 确认配置文件格式正确
- 检查 `log_level` 字段的值是否为支持的等级
- 确认应用程序已重新启动

### 问题：日志文件过大
- 检查日志轮换设置
- 考虑提高日志等级以减少输出
- 定期清理旧的日志文件
