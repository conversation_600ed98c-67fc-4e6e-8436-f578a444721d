{"files.associations": {"vector": "cpp", "dense": "cpp", "ostream": "cpp", "array": "cpp", "atomic": "cpp", "hash_map": "cpp", "bit": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "random": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "semaphore": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "condition_variable": "cpp", "iomanip": "cpp", "mutex": "cpp", "bitset": "cpp", "codecvt": "cpp", "optional": "cpp", "regex": "cpp", "cinttypes": "cpp", "__nullptr": "cpp", "__hash_table": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "filesystem": "cpp", "ios": "cpp", "queue": "cpp", "stack": "cpp", "__bit_reference": "cpp", "charconv": "cpp", "__node_handle": "cpp", "any": "cpp", "forward_list": "cpp", "ranges": "cpp", "valarray": "cpp", "__locale": "cpp", "unordered_set": "cpp", "csignal": "cpp", "shared_mutex": "cpp", "__config": "cpp"}}