#include "logger.hpp"
#include "config.hpp"
#include <iostream>

// 演示如何使用新的日志等级配置功能
int main() {
    std::cout << "=== 日志等级配置使用示例 ===" << std::endl;
    
    // 方法1: 直接从配置文件初始化日志
    std::cout << "\n1. 从配置文件初始化日志..." << std::endl;
    if (initLoggerFromConfig("config/debug_config.json")) {
        std::cout << "日志初始化成功" << std::endl;
    } else {
        std::cout << "日志初始化失败，使用默认配置" << std::endl;
    }
    
    // 测试不同等级的日志输出
    std::cout << "\n2. 测试不同等级的日志输出..." << std::endl;
    spdlog::trace("这是 TRACE 级别的日志 - 最详细的调试信息");
    spdlog::debug("这是 DEBUG 级别的日志 - 调试信息");
    spdlog::info("这是 INFO 级别的日志 - 一般信息");
    spdlog::warn("这是 WARN 级别的日志 - 警告信息");
    spdlog::error("这是 ERROR 级别的日志 - 错误信息");
    spdlog::critical("这是 CRITICAL 级别的日志 - 严重错误");
    
    // 方法2: 使用ConfigManager手动配置
    std::cout << "\n3. 使用ConfigManager手动配置日志..." << std::endl;
    try {
        ConfigManager config("config/custom_config.json");
        auto log_level = config.get<std::string>("/io_settings/logging/log_level");
        auto log_path = config.get<std::string>("/io_settings/logging/log_file_path");
        
        if (log_level && log_path) {
            std::cout << "配置文件中的日志等级: " << *log_level << std::endl;
            std::cout << "配置文件中的日志路径: " << *log_path << std::endl;
            
            // 重新配置日志系统
            spdlog::level::level_enum file_level = stringToLogLevel(*log_level);
            initLogger(*log_path, file_level);
            
            spdlog::info("日志系统已重新配置");
        }
    } catch (const std::exception& e) {
        std::cout << "配置失败: " << e.what() << std::endl;
    }
    
    // 方法3: 程序化设置不同等级
    std::cout << "\n4. 程序化设置不同日志等级..." << std::endl;
    
    std::vector<std::pair<std::string, spdlog::level::level_enum>> levels = {
        {"trace", spdlog::level::trace},
        {"debug", spdlog::level::debug},
        {"info", spdlog::level::info},
        {"warn", spdlog::level::warn},
        {"error", spdlog::level::err}
    };
    
    for (const auto& [level_name, level_enum] : levels) {
        std::cout << "设置日志等级为: " << level_name << std::endl;
        initLogger("logs/example_" + level_name + ".log", level_enum);
        
        // 测试输出
        spdlog::trace("TRACE 消息 (当前等级: " + level_name + ")");
        spdlog::debug("DEBUG 消息 (当前等级: " + level_name + ")");
        spdlog::info("INFO 消息 (当前等级: " + level_name + ")");
        spdlog::warn("WARN 消息 (当前等级: " + level_name + ")");
        spdlog::error("ERROR 消息 (当前等级: " + level_name + ")");
    }
    
    std::cout << "\n=== 示例完成 ===" << std::endl;
    std::cout << "请检查 logs/ 目录下的日志文件以查看不同等级的输出效果" << std::endl;
    
    return 0;
}
