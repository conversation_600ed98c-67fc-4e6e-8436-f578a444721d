#pragma once

#include "nlohmann/json.hpp"
#include <string>
#include <optional>
#include <shared_mutex>

class ConfigManager {
    public:
        // 构造函数，加载默认或自定义配置文件
        ConfigManager(const std::string& config_path = "");

        // 保存配置到文件
        bool saveConfig(const std::string& config_path = "");
        
        // 获取配置
        template<typename T>
        std::optional<T> get(const std::string& key) const;

        // 设置配置
        template<typename T>
        void set(const std::string& key, const T& value);

        // 获取完整配置
        const nlohmann::json& getConfig() const;

        // 合并其他配置
        void mergeConfig(const nlohmann::json& other_config);

    private:
        // 配置数据
        nlohmann::json config_data_;
        std::string loaded_config_path_;

        // 多线程安全
        mutable std::shared_mutex config_mutex_;

        void loadDefaultConfig();
        bool loadJsonFromFile(const std::string& config_path, nlohmann::json& out_json);
};