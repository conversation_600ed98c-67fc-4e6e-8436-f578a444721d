#pragma once
#include <spdlog/spdlog.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>

// 日志等级字符串转换函数
spdlog::level::level_enum stringToLogLevel(const std::string& level_str);

void initLogger(const std::string& log_path, spdlog::level::level_enum file_level = spdlog::level::info);
void initLogger();  // 使用默认配置的版本
bool initLoggerFromConfig(const std::string& config_path);  // 直接从配置文件初始化