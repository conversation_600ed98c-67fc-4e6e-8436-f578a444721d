#!/bin/bash

echo "编译和测试日志等级配置功能..."

# 设置编译参数
CXX=g++
CXXFLAGS="-std=c++17 -O2 -Wall -Wextra"
INCLUDES="-Iinclude -Isrc -I/usr/include/eigen3"
LIBS="-lspdlog -lpthread"

# 创建logs目录
mkdir -p logs

echo "编译日志配置测试程序..."

# 编译测试程序
$CXX $CXXFLAGS $INCLUDES -o test_log_level_config \
    test/test_log_level_config.cpp \
    src/logger.cpp \
    src/config.cpp \
    $LIBS

if [ $? -eq 0 ]; then
    echo "编译成功，运行测试..."
    ./test_log_level_config
    
    echo ""
    echo "=== 检查生成的日志文件 ==="
    
    if [ -f "logs/test_runtime.log" ]; then
        echo "主测试日志文件内容:"
        echo "--- logs/test_runtime.log ---"
        tail -20 logs/test_runtime.log
        echo ""
    fi
    
    echo "所有生成的日志文件:"
    ls -la logs/test_*.log 2>/dev/null || echo "没有找到测试日志文件"
    
    echo ""
    echo "=== 验证不同等级的日志输出 ==="
    
    for level in trace debug info warn error; do
        if [ -f "logs/test_${level}.log" ]; then
            echo "--- ${level} 等级日志文件 (logs/test_${level}.log) ---"
            cat "logs/test_${level}.log" | head -10
            echo ""
        fi
    done
    
    # 清理临时文件
    echo "清理临时文件..."
    rm -f test_config.json temp_config_*.json
    
else
    echo "编译失败"
    exit 1
fi

echo "测试完成!"
