# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

# Include any dependencies generated for this target.
include CMakeFiles/SCR_5000_AI.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/SCR_5000_AI.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/SCR_5000_AI.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SCR_5000_AI.dir/flags.make

CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o: ../src/KalmanFilter3D.cpp
CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/KalmanFilter3D.cpp

CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/KalmanFilter3D.cpp > CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/KalmanFilter3D.cpp -o CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o: ../src/PointTracker.cpp
CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/PointTracker.cpp

CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/PointTracker.cpp > CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/PointTracker.cpp -o CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o: ../src/comprehensive_test.cpp
CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/comprehensive_test.cpp

CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/comprehensive_test.cpp > CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/comprehensive_test.cpp -o CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o: ../src/config.cpp
CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/config.cpp

CMakeFiles/SCR_5000_AI.dir/src/config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/config.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/config.cpp > CMakeFiles/SCR_5000_AI.dir/src/config.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/config.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/config.cpp -o CMakeFiles/SCR_5000_AI.dir/src/config.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o: ../src/debug_tools.cpp
CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/debug_tools.cpp

CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/debug_tools.cpp > CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/debug_tools.cpp -o CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o: ../src/exception_safety.cpp
CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/exception_safety.cpp

CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/exception_safety.cpp > CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/exception_safety.cpp -o CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o: ../src/fft_gpu.cpp
CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/fft_gpu.cpp

CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/fft_gpu.cpp > CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/fft_gpu.cpp -o CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o: ../src/infer_engine.cpp
CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp

CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp > CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/infer_engine.cpp -o CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o: ../src/libSCR_5000_Alg.cpp
CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/libSCR_5000_Alg.cpp

CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/libSCR_5000_Alg.cpp > CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/libSCR_5000_Alg.cpp -o CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o: ../src/logger.cpp
CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/logger.cpp

CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/logger.cpp > CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/logger.cpp -o CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o: ../src/memory_pool.cpp
CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/memory_pool.cpp

CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/memory_pool.cpp > CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/memory_pool.cpp -o CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o: ../src/performance_optimizer.cpp
CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/performance_optimizer.cpp

CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/performance_optimizer.cpp > CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/performance_optimizer.cpp -o CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o: ../src/postprocess.cpp
CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp

CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp > CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/postprocess.cpp -o CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o: ../src/preprocess.cpp
CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp

CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp > CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/preprocess.cpp -o CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o: ../src/resource_manager.cpp
CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/resource_manager.cpp

CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/resource_manager.cpp > CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/resource_manager.cpp -o CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o: ../src/unified_resource_manager.cpp
CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/unified_resource_manager.cpp

CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/unified_resource_manager.cpp > CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/unified_resource_manager.cpp -o CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.s

CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o: CMakeFiles/SCR_5000_AI.dir/flags.make
CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o: ../src/utils.cpp
CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o: CMakeFiles/SCR_5000_AI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o -MF CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o.d -o CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp

CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp > CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.i

CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/src/utils.cpp -o CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.s

# Object files for target SCR_5000_AI
SCR_5000_AI_OBJECTS = \
"CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o" \
"CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o"

# External object files for target SCR_5000_AI
SCR_5000_AI_EXTERNAL_OBJECTS =

libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/comprehensive_test.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/debug_tools.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/exception_safety.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/memory_pool.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/performance_optimizer.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/resource_manager.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/unified_resource_manager.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/build.make
libSCR_5000_AI.so: /usr/local/lib/libopencv_gapi.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_highgui.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_ml.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_objdetect.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_photo.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_stitching.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_video.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_videoio.so.4.8.1
libSCR_5000_AI.so: /usr/local/cuda/lib64/libcudart_static.a
libSCR_5000_AI.so: /usr/lib/x86_64-linux-gnu/librt.a
libSCR_5000_AI.so: /home/<USER>/My_APP/TensorRT-8.6.1.6/lib/libnvinfer.so
libSCR_5000_AI.so: /home/<USER>/My_APP/TensorRT-8.6.1.6/lib/libnvinfer_plugin.so
libSCR_5000_AI.so: /usr/local/cuda/lib64/libcufft.so
libSCR_5000_AI.so: /usr/local/lib/libopencv_imgcodecs.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_dnn.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_calib3d.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_features2d.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_flann.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_imgproc.so.4.8.1
libSCR_5000_AI.so: /usr/local/lib/libopencv_core.so.4.8.1
libSCR_5000_AI.so: /usr/lib/gcc/x86_64-linux-gnu/11/libgomp.so
libSCR_5000_AI.so: /usr/lib/x86_64-linux-gnu/libpthread.a
libSCR_5000_AI.so: CMakeFiles/SCR_5000_AI.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Linking CXX shared library libSCR_5000_AI.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/SCR_5000_AI.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/SCR_5000_AI.dir/build: libSCR_5000_AI.so
.PHONY : CMakeFiles/SCR_5000_AI.dir/build

CMakeFiles/SCR_5000_AI.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/SCR_5000_AI.dir/cmake_clean.cmake
.PHONY : CMakeFiles/SCR_5000_AI.dir/clean

CMakeFiles/SCR_5000_AI.dir/depend:
	cd /home/<USER>/My_Project/MSHNet_TensorRT_Test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles/SCR_5000_AI.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/SCR_5000_AI.dir/depend

