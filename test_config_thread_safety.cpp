#include "config.hpp"
#include <thread>
#include <vector>
#include <iostream>
#include <chrono>
#include <atomic>

// 测试 ConfigManager 的多线程安全性
void test_config_thread_safety() {
    std::cout << "Testing ConfigManager thread safety..." << std::endl;
    
    // 创建配置管理器
    ConfigManager config("config/custom_config.json");
    
    // 原子计数器用于统计操作次数
    std::atomic<int> read_count{0};
    std::atomic<int> write_count{0};
    
    const int num_threads = 8;
    const int operations_per_thread = 1000;
    
    std::vector<std::thread> threads;
    
    // 创建读线程
    for (int i = 0; i < num_threads / 2; ++i) {
        threads.emplace_back([&config, &read_count, operations_per_thread]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                // 并发读取配置
                auto max_age = config.get<int>("/algorithm_settings/tracker/max_age");
                auto threshold = config.get<float>("/algorithm_settings/tracker/distance_threshold");
                auto log_level = config.get<std::string>("/io_settings/logging/log_level");
                
                if (max_age && threshold && log_level) {
                    read_count.fetch_add(1, std::memory_order_relaxed);
                }
                
                // 小延迟模拟实际使用
                std::this_thread::sleep_for(std::chrono::microseconds(1));
            }
        });
    }
    
    // 创建写线程
    for (int i = 0; i < num_threads / 2; ++i) {
        threads.emplace_back([&config, &write_count, operations_per_thread, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                // 并发写入配置
                config.set<int>("/algorithm_settings/tracker/max_age", 60 + i + j % 10);
                config.set<float>("/algorithm_settings/tracker/distance_threshold", 100.0f + i + j % 50);
                
                write_count.fetch_add(1, std::memory_order_relaxed);
                
                // 小延迟模拟实际使用
                std::this_thread::sleep_for(std::chrono::microseconds(1));
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    std::cout << "Thread safety test completed!" << std::endl;
    std::cout << "Total read operations: " << read_count.load() << std::endl;
    std::cout << "Total write operations: " << write_count.load() << std::endl;
    
    // 验证最终状态
    auto final_max_age = config.get<int>("/algorithm_settings/tracker/max_age");
    auto final_threshold = config.get<float>("/algorithm_settings/tracker/distance_threshold");
    
    if (final_max_age && final_threshold) {
        std::cout << "Final max_age: " << *final_max_age << std::endl;
        std::cout << "Final distance_threshold: " << *final_threshold << std::endl;
    }
}

// 测试 mergeConfig 的线程安全性
void test_merge_config_thread_safety() {
    std::cout << "\nTesting mergeConfig thread safety..." << std::endl;
    
    ConfigManager config("config/custom_config.json");
    
    const int num_threads = 4;
    std::vector<std::thread> threads;
    
    // 创建多个线程同时进行 merge 操作
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&config, i]() {
            nlohmann::json merge_data;
            merge_data["test_section"]["thread_" + std::to_string(i)] = {
                {"value", i * 100},
                {"name", "thread_" + std::to_string(i)}
            };
            
            for (int j = 0; j < 100; ++j) {
                config.mergeConfig(merge_data);
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        });
    }
    
    // 同时进行读取操作
    std::thread reader([&config]() {
        for (int i = 0; i < 1000; ++i) {
            auto full_config = config.getConfig();
            // 简单访问以确保读取正常
            if (!full_config.empty()) {
                // 读取成功
            }
            std::this_thread::sleep_for(std::chrono::microseconds(1));
        }
    });
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    reader.join();
    
    std::cout << "mergeConfig thread safety test completed!" << std::endl;
    
    // 检查最终配置
    auto final_config = config.getConfig();
    if (final_config.contains("test_section")) {
        std::cout << "Test section successfully merged" << std::endl;
    }
}

int main() {
    try {
        test_config_thread_safety();
        test_merge_config_thread_safety();
        std::cout << "\nAll thread safety tests passed!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
